/**
 * Servicio de Azure Voices para síntesis de voz
 */

import axios from "axios";
import { log } from "../LogService";
import { handleRequest } from "../_helpersService";
import type { IVoicesService, AudioRequest, PerformanceStats } from "./interfaces";
import { SPEECH_CONFIG, VOICE_DEFAULTS, getAzureHeaders, validateConfig } from "./config";
import { AudioCache } from "./cache";
import { RetryQueue } from "./retryQueue";
import { optimizeTextForSpeech, wrapInSSML, createVoiceParams, validateTextForSpeech } from "./textUtils";

export class AzureVoicesService implements IVoicesService {
  private static instance: AzureVoicesService;
  
  // ===== CORE =====
  private voiceId: string = "";
  private genre: string = "";
  private availableVoicesList: string[] = [];
  
  // ===== SISTEMAS =====
  private audioCache: AudioCache;
  private retryQueue: RetryQueue;

  private constructor() {
    this.audioCache = new AudioCache();
    this.retryQueue = new RetryQueue();
  }

  public static getInstance(): AzureVoicesService {
    if (!AzureVoicesService.instance) {
      AzureVoicesService.instance = new AzureVoicesService();
    }
    return AzureVoicesService.instance;
  }

  // ========== GETTERS Y SETTERS ==========
  public getCurrentVoiceId(): string {
    return this.voiceId;
  }

  public getAvailableVoicesList(): string[] {
    return this.availableVoicesList;
  }

  private setVoiceId(voice: string): void {
    this.voiceId = voice;
  }

  private setGenre(genre: string): void {
    this.genre = genre;
  }

  // ========== CONFIGURACIÓN Y ESTADO ==========
  public reset(): void {
    log.info("AzureVoicesService", "🔄 Reseteando servicio...");

    // Core
    this.voiceId = "";
    this.genre = "";
    this.availableVoicesList = [];

    // Sistemas
    this.audioCache.clear();
    this.retryQueue.clear();
  }

  // ========== API DE AZURE ==========
  public async getAvailableVoices(): Promise<string[]> {
    validateConfig();

    const data = {
      language: SPEECH_CONFIG.LANGUAGE,
      gender: this.genre,
    };

    return handleRequest<string[]>(
      axios.post(`${SPEECH_CONFIG.BASE_URL}available_voices`, data, {
        headers: getAzureHeaders(),
      }),
    );
  }

  public async configVoice(genre: string): Promise<boolean> {
    this.setGenre(genre);

    try {
      const voices = await this.getAvailableVoices();
      this.availableVoicesList = voices;

      if (voices.length > 0) {
        const randomVoice = voices[Math.floor(Math.random() * voices.length)];
        this.setVoiceId(randomVoice);
        log.success("AzureVoicesService", `Voz configurada: ${randomVoice}`);
        return true;
      }

      log.warn("AzureVoicesService", "No hay voces disponibles");
      return false;
    } catch (error) {
      log.error("AzureVoicesService", "Error configurando voz", error);
      this.availableVoicesList = [];
      return false;
    }
  }

  // ========== MAIN ==========
  public async getAudio(text: string): Promise<Blob> {
    validateConfig();
    
    if (!this.voiceId) {
      throw new Error("Azure Speech no configurado correctamente");
    }

    if (!validateTextForSpeech(text)) {
      throw new Error("Texto no válido para síntesis");
    }

    // Verificar cache primero
    const cachedAudio = this.audioCache.get(text, this.voiceId);
    if (cachedAudio) {
      return cachedAudio;
    }

    // Optimización de texto
    const optimizedText = optimizeTextForSpeech(text);

    return this.retryQueue.handleThrottling(async () => {
      const audioRequest: AudioRequest = {
        input_text: optimizedText,
        voice_params: createVoiceParams(this.voiceId),
        output_format: VOICE_DEFAULTS.OUTPUT_FORMAT,
        ssml: wrapInSSML(optimizedText, this.voiceId)
      };

      const response = await handleRequest<Blob>(
        axios.post(`${SPEECH_CONFIG.BASE_URL}t2s`, audioRequest, {
          responseType: "blob",
          timeout: SPEECH_CONFIG.REQUEST_TIMEOUT,
          headers: getAzureHeaders()
        })
      );

      // Guardar en cache
      this.audioCache.set(text, this.voiceId, response);

      return response;
    });
  }

  // ========== UTILIDADES ==========
  public cleanupCache(): void {
    this.audioCache.cleanup();
  }

  public getPerformanceStats(): PerformanceStats {
    return {
      cache: this.audioCache.getStats(),
      queue: this.retryQueue.getStats(),
      voice: {
        configured: Boolean(this.voiceId),
        voiceId: this.voiceId,
        availableCount: this.availableVoicesList.length
      }
    };
  }
}
