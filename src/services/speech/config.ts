/**
 * Configuración para el servicio de Azure Speech
 */

export const SPEECH_CONFIG = {
  LANGUAGE: "es",
  BASE_URL: import.meta.env.VITE_SPEECH_API_URL,
  API_KEY: import.meta.env.VITE_SPEECH_API_KEY,
  CACHE_TTL: 24 * 60 * 60 * 1000, // 24 horas
  REQUEST_TIMEOUT: 30000, // 30 segundos
  RETRY_DELAY: 1000, // 1 segundo entre reintentos
} as const;

export const VOICE_DEFAULTS = {
  RATE: 1.1,
  PITCH: "default",
  VOLUME: "default",
  STYLE: "general",
  OUTPUT_FORMAT: "mp3",
} as const;

export const SSML_CONFIG = {
  VERSION: "1.0",
  XMLNS: "http://www.w3.org/2001/10/synthesis",
  LANG: "es-ES",
  BREAK_TIME: "500ms",
} as const;

/**
 * Obtiene los headers para las peticiones a Azure
 */
export function getAzureHeaders(): Record<string, string> {
  if (!SPEECH_CONFIG.API_KEY) {
    throw new Error("VITE_SPEECH_API_KEY no está configurada");
  }
  
  return {
    Authorization: `Bearer ${SPEECH_CONFIG.API_KEY}`,
    "Content-Type": "application/json",
  };
}

/**
 * Valida que la configuración esté completa
 */
export function validateConfig(): void {
  if (!SPEECH_CONFIG.BASE_URL) {
    throw new Error("VITE_SPEECH_API_URL no está configurada");
  }
  
  if (!SPEECH_CONFIG.API_KEY) {
    throw new Error("VITE_SPEECH_API_KEY no está configurada");
  }
}
