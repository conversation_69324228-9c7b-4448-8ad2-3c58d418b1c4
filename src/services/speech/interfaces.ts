/**
 * Interfaces y tipos para el sistema de Speech
 */

export interface IVoicesService {
  getAvailableVoices(): Promise<string[]>;
  getAudio(text: string): Promise<Blob>;
  configVoice(genre: string): Promise<boolean>;
  getCurrentVoiceId(): string;
  getAvailableVoicesList(): string[];
  reset(): void;
  cleanupCache(): void;
  getPerformanceStats(): object;
}

export interface VoiceParams {
  voice_id: string;
  rate: number;
  pitch: string;
  volume: string;
  style: string;
}

export interface AudioRequest {
  input_text: string;
  voice_params: VoiceParams;
  output_format: string;
  ssml: string;
}

export interface PerformanceStats {
  cache: {
    size: number;
    hitRate: number;
    ttl: number;
  };
  queue: {
    length: number;
    isProcessing: boolean;
  };
  voice: {
    configured: boolean;
    voiceId: string;
    availableCount: number;
  };
}

export interface RetryRequest<T> {
  (): Promise<T>;
}
