# Sistema de Speech - Arquitectura Modular

Este directorio contiene el sistema de Speech refactorizado en módulos independientes para mejorar la mantenibilidad y organización del código.

## Estructura de Archivos

### 📁 Archivos Principales

- **`index.ts`** - Punto de entrada principal que exporta todo el sistema
- **`speechService.ts`** - Servicio principal de Speech (manejo de reproducción y audio)
- **`azureVoicesService.ts`** - Servicio específico para Azure Voices API

### 📁 Módulos de Soporte

- **`interfaces.ts`** - Interfaces y tipos TypeScript
- **`config.ts`** - Configuración y constantes de Azure Speech
- **`cache.ts`** - Sistema de cache para audio
- **`retryQueue.ts`** - Sistema de cola de reintentos para throttling
- **`textUtils.ts`** - Utilidades para procesamiento de texto y SSML

## Responsabilidades por Módulo

### 🎯 SpeechService (speechService.ts)
- Manejo del elemento HTML Audio
- Control de reproducción (play, stop, pause)
- Detección de interacción del usuario
- Manejo de autoplay bloqueado
- Métodos de conveniencia (speak, toSpeech, etc.)

### 🎯 AzureVoicesService (azureVoicesService.ts)
- Comunicación con Azure Speech API
- Configuración de voces
- Generación de audio desde texto
- Integración con sistemas de cache y retry

### 🎯 AudioCache (cache.ts)
- Cache en memoria para audio generado
- TTL (Time To Live) configurable
- Limpieza automática de entradas expiradas
- Estadísticas de rendimiento

### 🎯 RetryQueue (retryQueue.ts)
- Manejo de throttling de Azure (error 429)
- Cola de reintentos con delays configurables
- Procesamiento secuencial para evitar sobrecarga

### 🎯 TextUtils (textUtils.ts)
- Optimización de texto para síntesis
- Generación de SSML (Speech Synthesis Markup Language)
- Validación de texto
- Sanitización para SSML

### 🎯 Config (config.ts)
- Configuración centralizada de Azure
- Constantes del sistema
- Validación de configuración
- Headers para peticiones HTTP

## Uso

### Importación Simple
```typescript
import { speechService } from './services/speech';

// Usar el servicio
await speechService.speak("Hola mundo");
```

### Importación Específica
```typescript
import { SpeechService, AzureVoicesService } from './services/speech';

const speech = new SpeechService();
const azure = AzureVoicesService.getInstance();
```

### Configuración
```typescript
import { speechService } from './services/speech';

// Configurar voz
await speechService.configVoice("female");

// Hablar con auto-configuración
await speechService.speakWithAutoConfig("Texto a sintetizar", "male");
```

## Compatibilidad

El archivo `src/services/SpeechService.ts` original mantiene la compatibilidad completa re-exportando todo desde este módulo. No se requieren cambios en el código existente.

## Beneficios de la Refactorización

1. **Separación de Responsabilidades**: Cada módulo tiene una responsabilidad específica
2. **Mantenibilidad**: Código más fácil de mantener y debuggear
3. **Testabilidad**: Módulos independientes más fáciles de testear
4. **Reutilización**: Componentes pueden usarse independientemente
5. **Escalabilidad**: Fácil añadir nuevas funcionalidades sin afectar otros módulos

## Variables de Entorno Requeridas

```env
VITE_SPEECH_API_URL=https://your-azure-speech-api.com/
VITE_SPEECH_API_KEY=your-api-key-here
```
