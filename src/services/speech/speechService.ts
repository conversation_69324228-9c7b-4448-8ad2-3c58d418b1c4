/**
 * Servicio principal de Speech - Maneja la reproducción y control de audio
 */

import { log } from "../LogService";
import { AzureVoicesService } from "./azureVoicesService";
import type { IVoicesService } from "./interfaces";

export class SpeechService {
  private azureService: IVoicesService;
  private audioElement: HTMLAudioElement | null = null;
  private serviceName = "speechService";
  private userHasInteracted = false;

  // ========== CONSTRUCTOR E INICIALIZACIÓN ==========
  constructor() {
    this.azureService = AzureVoicesService.getInstance();
    this.initializeAudioElement();
    this.setupUserInteractionDetection();
  }

  private initializeAudioElement(): void {
    this.audioElement = document.getElementById("audio") as HTMLAudioElement;

    if (!this.audioElement) {
      this.audioElement = document.createElement("audio");
      this.audioElement.id = "audio";
      this.audioElement.preload = "metadata";
      document.body.appendChild(this.audioElement);
    }

    log.debug(this.serviceName, "🔊 Elemento de audio inicializado");
  }

  private setupUserInteractionDetection(): void {
    // Detectar primera interacción del usuario
    const events = ['click', 'touchstart', 'keydown'];

    const handleFirstInteraction = () => {
      this.userHasInteracted = true;
      log.info(this.serviceName, "👆 Primera interacción del usuario detectada");

      // Remover listeners después de la primera interacción
      events.forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };

    events.forEach(event => {
      document.addEventListener(event, handleFirstInteraction, { once: true });
    });
  }

  // ========== GETTERS Y ESTADO ==========
  private getAudioElement(): HTMLAudioElement {
    if (!this.audioElement) {
      this.initializeAudioElement();
    }
    return this.audioElement!;
  }

  public getCurrentVoiceId(): string {
    return this.azureService.getCurrentVoiceId();
  }

  public getAvailableVoicesList(): string[] {
    return this.azureService.getAvailableVoicesList();
  }

  // ========== CONFIGURACIÓN DE VOZ ==========
  public async configVoice(genre: string): Promise<boolean> {
    try {
      log.info(this.serviceName, `🔧 Configurando voz: ${genre}`);
      const success = await this.azureService.configVoice(genre);

      if (success) {
        const voiceId = this.azureService.getCurrentVoiceId();
        log.success(this.serviceName, `✅ Voz configurada: ${voiceId}`);
      } else {
        log.warn(this.serviceName, "❌ No se pudo configurar la voz");
      }

      return success;
    } catch (error) {
      log.error(this.serviceName, "Error configurando voz", error);
      return false;
    }
  }

  // ========== API PRINCIPAL ==========
  public async getAudio(message: string): Promise<Blob> {
    try {
      return await this.azureService.getAudio(message);
    } catch (error) {
      log.error(this.serviceName, "Error obteniendo audio", error);
      throw error;
    }
  }

  public async getSpeech(message: string): Promise<string> {
    try {
      const audioBlob = await this.getAudio(message);
      const audioUrl = URL.createObjectURL(audioBlob);
      return audioUrl;
    } catch (error) {
      log.error(this.serviceName, "Error obteniendo URL de speech", error);
      throw error;
    }
  }

  // ========== CONTROL DE REPRODUCCIÓN ==========
  public async playSpeech(): Promise<void> {
    const audioElement = this.getAudioElement();
    audioElement.volume = 1;

    try {
      await audioElement.play();
    } catch (error) {
      if (error instanceof Error && error.name === 'NotAllowedError') {
        this.handleAutoplayBlocked();
      } else {
        throw error;
      }
    }
  }

  public stopSpeech(): void {
    const audioElement = this.getAudioElement();
    audioElement.volume = 0;
    audioElement.pause();
  }

  public noSpeech(): void {
    this.stopSpeech();
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = "";
  }

  // ========== MANEJO DE AUTOPLAY ==========
  private handleAutoplayBlocked(): void {
    if (!this.userHasInteracted) {
      this.showAudioPermissionNotification();
      this.setupRetryOnInteraction();
    }
  }

  private setupRetryOnInteraction(): void {
    const playOnInteraction = async () => {
      try {
        const audioElement = this.getAudioElement();
        if (audioElement.src && audioElement.readyState >= 2) {
          await audioElement.play();
          log.success(this.serviceName, "✅ Audio reproducido tras interacción");
        }
      } catch (error) {
        log.error(this.serviceName, "Error reproduciendo audio tras interacción", error);
      }
    };

    const events = ['click', 'touchstart', 'keydown'];
    events.forEach(event => {
      document.addEventListener(event, playOnInteraction, { once: true });
    });
  }

  private showAudioPermissionNotification(): void {
    if (import.meta.env.MODE === "development") {
      log.info(this.serviceName, "🔔 Esperando interacción del usuario para reproducir audio");
    }
  }

  // ========== MÉTODOS DE CONVENIENCIA ==========
  public setSpeech(url: string): void {
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = url;
    audioElement.currentTime = 0;
    audioElement.volume = 1;
  }

  public async toSpeech(url: string): Promise<void> {
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = url;
    audioElement.currentTime = 0;
    audioElement.volume = 1;

    try {
      await audioElement.play();
    } catch (error) {
      if (error instanceof Error && error.name === 'NotAllowedError') {
        this.handleAutoplayBlocked();
      } else {
        throw error;
      }
    }
  }

  public async speak(text: string): Promise<void> {
    try {
      const audioUrl = await this.getSpeech(text);
      await this.toSpeech(audioUrl);
    } catch (error) {
      log.error(this.serviceName, "Error en speak", error);
      throw error;
    }
  }

  // ========== AUTO-CONFIGURACIÓN ==========
  public async speakWithAutoConfig(text: string, genre: string = "female"): Promise<void> {
    try {
      // Auto-configurar si no está configurado
      if (!this.getCurrentVoiceId()) {
        log.info(this.serviceName, "🔧 Auto-configurando voz...");
        const configured = await this.configVoice(genre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      await this.speak(text);
    } catch (error) {
      log.error(this.serviceName, "Error en speakWithAutoConfig", error);
      throw error;
    }
  }

  // ========== UTILIDADES Y LIMPIEZA ==========
  public cleanup(): void {
    this.noSpeech();
    this.azureService.cleanupCache();

    if (this.audioElement && this.audioElement.parentNode) {
      this.audioElement.parentNode.removeChild(this.audioElement);
      this.audioElement = null;
    }

    log.info(this.serviceName, "🧹 Servicio limpiado");
  }
}
